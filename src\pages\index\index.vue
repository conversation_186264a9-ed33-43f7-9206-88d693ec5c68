<template>
  <view class="u-page">
    <up-checkbox-group>
      <up-checkbox>
        <template v-slot:label>
          <view>是的</view>
          <up-input placeholder="请输入内容" border="bottom" clearable></up-input>
        </template>
      </up-checkbox>
    </up-checkbox-group>
    <up-tabbar :fixed="true" :placeholder="true" :safeAreaInsetBottom="true">
      <up-tabbar-item text="首页" icon="home"></up-tabbar-item>
      <up-tabbar-item text="放映厅" icon="photo"></up-tabbar-item>
      <up-tabbar-item text="" icon="plus" mode="midButton"></up-tabbar-item>
      <up-tabbar-item text="直播" icon="play-right"></up-tabbar-item>
      <up-tabbar-item text="我的" icon="account"></up-tabbar-item>
    </up-tabbar>
  </view>
</template>

<script setup lang="ts"></script>

<style scoped lang="scss">
  :deep {
    .u-tabbar-item__icon--mid-button {
      min-height: 70px;
      max-height: 70px;
    }
    .u-checkbox {
      align-items: baseline !important;
    }
  }
  // .u-page {
  //   padding: 0;
  //   &__item {
  //     &__title {
  //       color: $u-tips-color;
  //       background-color: $u-bg-color;
  //       padding: 15px 15px 5px 15px;
  //       font-size: 15px;

  //       &__slot-title {
  //         color: $u-primary;
  //         font-size: 14px;
  //       }
  //     }

  //     &__slot-icon {
  //       width: 17px;
  //       height: 17px;
  //     }
  //   }
  // }
</style>
